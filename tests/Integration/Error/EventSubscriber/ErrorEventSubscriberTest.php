<?php

declare(strict_types=1);

namespace Tests\Integration\Error\EventSubscriber;

use App\Error\EventSubscriber\ErrorEventSubscriber;
use App\JsonTemplate\Event\JsonTemplateHandledEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class ErrorEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            JsonTemplateHandledEvent::NAME,
            ErrorEventSubscriber::class,
            'onJsonTemplateHandled',
        );
    }
}
