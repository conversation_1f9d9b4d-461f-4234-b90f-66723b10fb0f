<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Url\EventSubscriber;

use App\Http\Response\Event\ResponseCachingStartedEvent;
use App\Http\Url\EventSubscriber\StartResponseCachingEventSubscriber;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class StartResponseCachingEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            ResponseCachingStartedEvent::NAME,
            StartResponseCachingEventSubscriber::class,
            'disablePersistentParameters',
        );
    }
}
