<?php

declare(strict_types=1);

namespace Tests\Integration\Debug\EventSubscriber;

use App\Debug\EventSubscriber\DebugEventSubscriber;
use App\JsonTemplate\Event\JsonTemplateHandledEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class DebugEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            JsonTemplateHandledEvent::NAME,
            DebugEventSubscriber::class,
            'onJsonTemplateHandled',
        );
    }
}
