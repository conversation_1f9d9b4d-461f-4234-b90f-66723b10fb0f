<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\ComponentMonitor\Repository;

use App\Component\Generic\Group\GroupComponent;
use App\Component\Generic\Group\GroupLayout;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\ComponentNamespaceMapper;
use App\JsonTemplate\Component\ComponentResolverLocator;
use App\JsonTemplate\Component\File\ComponentFileLocator;
use App\JsonTemplate\Component\Layout\ComponentLayoutHelper;
use Visymo\DevelopBundle\ComponentMonitor\Component\ComponentLayoutState;
use Visymo\DevelopBundle\ComponentMonitor\Component\ComponentState;
use Visymo\DevelopBundle\JsonTemplateDiff\Normalizer\JsonTemplateNormalized;
use Visymo\DevelopBundle\JsonTemplateDiff\Result\JsonTemplateModuleResult;
use Visymo\DevelopBundle\JsonTemplateDiff\Result\JsonTemplateResultRegistry;

final readonly class ComponentStateRepository
{
    public function __construct(
        private JsonTemplateResultRegistry $jsonTemplateResultRegistry,
        private ComponentResolverLocator $componentResolverLocator,
        private ComponentLayoutHelper $componentLayoutHelper,
        private ComponentFileLocator $componentFileLocator,
        private ComponentNamespaceMapper $componentNamespaceMapper
    )
    {
    }

    /**
     * @return ComponentState[]
     */
    public function findAll(): array
    {
        $componentStates = [];

        foreach ($this->componentResolverLocator->getSupportedTypes() as $componentType) {
            $layoutStates = [];
            $componentLayoutCases = [];

            if ($componentType === GroupComponent::getType()) {
                $componentLayoutCases = GroupLayout::cases();
            } else {
                $componentLayout = $this->componentLayoutHelper->getComponentLayout($componentType);

                if ($componentLayout !== null) {
                    $componentLayoutCases = $componentLayout::cases();
                }
            }

            /** @var string[] $availableLayouts */
            $availableLayouts = array_column($componentLayoutCases, 'value');

            foreach ($availableLayouts as $availableLayout) {
                $filePath = $this->componentFileLocator->getComponentLayoutFile(
                    componentType: $componentType,
                    layout       : $availableLayout,
                )?->getRealFilePath();

                $layoutStates[$availableLayout] = new ComponentLayoutState(
                    layout  : $availableLayout,
                    filePath: $filePath,
                );
            }

            $filePath = $this->componentFileLocator->getComponentFile($componentType)->getRealFilePath();
            $componentState = new ComponentState(
                componentType: $componentType,
                filePath     : $filePath,
                layoutStates : $layoutStates,
            );

            /** @var class-string<ComponentInterface> $componentClass */
            $componentClass = $this->componentNamespaceMapper->getNamespace($componentType);
            $componentState->setIsInternal($componentClass::isInternal());

            $componentStates[$componentState->componentType] = $componentState;
        }

        ksort($componentStates);

        $this->scanJsonTemplateModules($componentStates);

        return array_values($componentStates);
    }

    /**
     * @param array<string, ComponentState> $componentStates
     */
    private function scanJsonTemplateModules(array $componentStates): void
    {
        foreach ($this->jsonTemplateResultRegistry->getModules() as $module) {
            $jsonTemplateModuleResult = $this->jsonTemplateResultRegistry->getModuleResult($module);

            $this->scanJsonTemplateModule($jsonTemplateModuleResult, $componentStates);
        }
    }

    /**
     * @param array<string, ComponentState> $componentStates
     */
    private function scanJsonTemplateModule(
        JsonTemplateModuleResult $jsonTemplateModuleResult,
        array $componentStates
    ): void
    {
        foreach ($jsonTemplateModuleResult->getResults() as $jsonTemplateResults) {
            foreach ($jsonTemplateResults as $jsonTemplateResult) {
                $this->scanJsonTemplate($jsonTemplateResult->jsonTemplateNormalized, $componentStates);
            }
        }
    }

    /**
     * @param array<string, ComponentState> $componentStates
     */
    private function scanJsonTemplate(
        JsonTemplateNormalized $jsonTemplateNormalized,
        array $componentStates
    ): void
    {
        foreach ($jsonTemplateNormalized->getComponentTypes() as $componentType) {
            $componentState = $componentStates[$componentType]->setInUse();

            foreach ($jsonTemplateNormalized->getComponentLayouts($componentType) as $layout) {
                $componentState->getLayoutState($layout)->setInUse();
            }
        }
    }
}
