<?php

declare(strict_types=1);

namespace App\Component\Generic\SearchHeaderContent;

use App\Brand\Settings\BrandSettingsHelper;
use App\ContentPage\Helper\ContentPageCategoryHelper;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Menu\Helper\MenuHelper;
use App\Search\Registry\RouteRegistry;
use App\SearchApi\SearchApiManager;
use Twig\Environment;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPageNestedCategory;

final class SearchHeaderContentRenderer extends AbstractComponentRenderer
{
    private const MAX_CONTENT_PAGE_CATEGORIES = 5;

    public function __construct(
        private readonly Environment $twig,
        private readonly SearchApiManager $searchApiManager,
        private readonly SearchHeaderContentSearchBarRenderer $searchBarRenderer,
        private readonly RouteRegistry $routeRegistry,
        private readonly BrandSettingsHelper $brandSettingsHelper,
        private readonly MenuHelper $menuHelper,
        private readonly ContentPageCategoryHelper $contentPageCategoryHelper
    )
    {
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof SearchHeaderContentComponent) {
            throw UnsupportedComponentException::create($component, [SearchHeaderContentComponent::class]);
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'view'                    => $view,
                'query'                   => $component->showSearchQuery
                    ? $view->getDataRegistry()->getQuery()
                    : null,
                'layout'                  => $component->layout->value,
                'logo_position'           => $component->logoPosition->value,
                'show_categories'         => $component->showCategories,
                'autofocus'               => $component->autofocus,
                'content_page_categories' => $this->getContentPageCategories($component, $view),
                'logo'                    => $component->layout->getLogo($component->logoDarkMode)?->value,
                'logo_style_filter'       => $component->logoStyleFilter,
                'search_bar'              => $this->searchBarRenderer->renderSearchBar($component, $view),
                'search_route'            => $this->routeRegistry->getSearchRoute(),
                'brand_name'              => $this->brandSettingsHelper->getSettings()->getName(),
                'menu_helper'             => $this->menuHelper,
            ],
        );
    }

    /**
     * @return ContentPageNestedCategory[]
     */
    private function getContentPageCategories(SearchHeaderContentComponent $component, ViewInterface $view): array
    {
        if (!$component->showCategories) {
            return [];
        }

        return $this->contentPageCategoryHelper->getPreferenceContentPageCategories(
            $view->getDataRegistry()->getContentPageCategories($component)->results,
            self::MAX_CONTENT_PAGE_CATEGORIES,
        );
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
    ): void
    {
        if (!$component instanceof SearchHeaderContentComponent) {
            throw UnsupportedComponentException::create($component, [SearchHeaderContentComponent::class]);
        }

        if ($component->showCategories) {
            $request
                ->setRequirements(
                    [
                        ViewDataProperty::CONTENT_PAGE_CATEGORIES,
                        ViewDataProperty::QUERY,
                    ],
                );
        } else {
            $request->setRequirements(
                [
                    ViewDataProperty::QUERY,
                ],
            );
        }
    }

    protected function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions,
    ): void
    {
        if (!$component instanceof SearchHeaderContentComponent) {
            throw UnsupportedComponentException::create($component, [SearchHeaderContentComponent::class]);
        }

        if (!$component->showCategories) {
            return;
        }

        $contentPageCategoriesViewDataRequest = $request
            ->contentPageCategories()
            ->enable()
            ->setMaxLevel(0)
            ->setHasImage(true)
            ->setIsAdult(false);

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::CONTENT_PAGE_CATEGORIES,
            searchApiViewDataRequest: $contentPageCategoriesViewDataRequest,
            conditions              : $conditions,
        );
    }
}
