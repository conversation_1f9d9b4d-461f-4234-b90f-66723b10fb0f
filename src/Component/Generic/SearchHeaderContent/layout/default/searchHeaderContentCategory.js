/**
 * @constructor
 */
function MoreCategories() {
    this._toggleActiveClass = 'search-header-content__navigation-more--active';
    this._categorySectionClass = 'section--search-category';
    this._componentHiddenClass = 'component--hidden';

    this.init();
}

MoreCategories.prototype.init = function () {
    this._searchHeaderElement = document.querySelector('.search-header-content');

    if (this._searchHeaderElement === null) {
        return;
    }

    this._toggleElement = this._searchHeaderElement.querySelector('.search-header-content__navigation-more');
    this._categorySectionElement = document.querySelector('.' + this._categorySectionClass);

    if (this._toggleElement === null) {
        return;
    }

    if (this._categorySectionElement === null) {
        return;
    }

    this.addEventListeners();
};

MoreCategories.prototype.addEventListeners = function () {
    window.addEventListener('resize', this.hide.bind(this));
    this._toggleElement.addEventListener('click', this.toggle.bind(this));

    var self = this;

    Helper.addDelegateEvent(
        'click',
        '.body',
        '.' + this._categorySectionClass + ', .search-header-content__navigation-more',
        function () {
        },
        function () {
            self.hide();
        }
    );
};

MoreCategories.prototype.hide = function () {
    this._toggleElement.classList.remove(this._toggleActiveClass);
    this._categorySectionElement.classList.add(this._componentHiddenClass);
};

MoreCategories.prototype.show = function () {
    var heightOfSearchSection = document.querySelector('[class*="section--search"]').getBoundingClientRect().height;

    this._toggleElement.classList.add(this._toggleActiveClass);

    this._categorySectionElement.style.marginTop = heightOfSearchSection + 'px';
    this._categorySectionElement.style.maxHeight = 'calc(100vh - ' + heightOfSearchSection + 'px)';
    this._categorySectionElement.classList.remove(this._componentHiddenClass);
};

MoreCategories.prototype.toggle = function () {
    if (this._toggleElement.classList.toggle(this._toggleActiveClass)) {
        this.show();
    } else {
        this.hide();
    }
};

appReady.push(function () {
    new MoreCategories();
});
