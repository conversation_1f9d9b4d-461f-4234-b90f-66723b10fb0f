@import "../../../SearchHeader/layout/default/searchHeaderMixins";

/** @define search-header-content */
.search-header-content--default {
    column-gap: 1.5rem;
    display: grid;
    padding-bottom: 1.5rem;
    padding-top: 1.5rem;
    position: relative;

    @media #{map-get($media-min, c)} {
        .search-header-content {
            &__categories {
                column-gap: 1.5rem;
                display: flex;
                flex-shrink: 1;
                flex-wrap: wrap;
                justify-content: flex-end;
            }

            &__category-more {
                margin-right: 1.5rem;
            }

            &--logo-left {
                grid-template-areas: "logo navigation navigation-more search";
                grid-template-columns: auto minmax(0, 1fr) auto 10rem;
            }

            &--logo-right {
                grid-template-areas: "search navigation navigation-more logo";
                grid-template-columns: 10rem minmax(0, 1fr) auto auto;
            }
        }
    }

    @media #{map-get($media-max, b)} {
        grid-template-areas: "logo" "search" "navigation" "navigation-more";
        grid-template-columns: auto;

        .search-header-content {
            &__logo {
                justify-self: center;
            }

            &__navigation {
                height: auto;
                overflow: visible;
            }

            &__categories {
                display: block;
                justify-content: flex-start;
            }
        }
    }

    @include search-header-search-bar-mobile-full-page;

    .search-header-content {
        &__logo {
            align-self: center;
            grid-area: logo;
        }

        &__brand-image {
            height: max-content;
            max-height: 4.4rem;
            max-width: 20rem;
            object-fit: scale-down;
        }

        // Favicon
        &__brand-icon {
            display: none;
        }

        &__brand-link {
            display: inline-block;
        }

        // Navigation (for category layouts)
        &__navigation {
            align-self: center;
            grid-area: navigation;
            height: 4rem;
            line-height: 4rem;
            overflow: hidden;
            padding-right: 0.4rem;
        }

        &__navigation-more {
            grid-area: navigation-more;
        }

        &__categories {
            font-size: 1.6rem;
            font-weight: 700;
        }

        &__category-link {
            color: var(--container__section_color, #000000);
            white-space: nowrap;

            &:hover {
                text-decoration: underline;
            }
        }

        &__category-more {
            color: var(--container__section_color, #000000);
            cursor: pointer;
            display: block;
            font-size: 1.6rem;
            font-weight: 700;
            line-height: 4rem;
            padding-right: 2rem;
            position: relative;
            user-select: none;

            &::before {
                color: var(--container__section_color, #555555);
                content: $vsi-chevron-down;
                position: absolute;
                right: 0;
                transition: 0.2s;
            }
        }

        &__navigation-more {
            /* stylelint-disable-next-line selector-class-pattern */
            &--active .search-header-content__category-more::before {
                transform: rotate(180deg);
            }
        }

        &__auto-suggest {
            --border-color: #dddddd;
            top: 100%;
        }
    }
}
