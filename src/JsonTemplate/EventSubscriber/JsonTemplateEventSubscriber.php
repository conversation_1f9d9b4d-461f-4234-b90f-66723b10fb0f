<?php

declare(strict_types=1);

namespace App\JsonTemplate\EventSubscriber;

use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

final class JsonTemplateEventSubscriber implements EventSubscriberInterface
{
    private const string HEADER_X_JSON_TEMPLATE_FILE = 'X-Log-Json_Template_File';

    public function __construct(
        private readonly string $projectDir
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            JsonTemplateViewCreatedEvent::NAME => 'onJsonTemplateViewCreated',
        ];
    }

    public function onJsonTemplateViewCreated(JsonTemplateViewCreatedEvent $event): void
    {
        $response = $event->view->getResponse();
        $jsonTemplate = $event->view->getJsonTemplate();
        $filePath = preg_replace(
            sprintf('~^%s~', preg_quote($this->projectDir, '~')),
            '',
            $jsonTemplate->filePath,
        );

        // Load balancer will catch these response headers and log them in access log
        $response->headers->set(
            self::HEADER_X_JSON_TEMPLATE_FILE,
            $filePath,
        );
    }
}
