<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\DataRequest;

use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\Data\ViewDataRegistry;

final class ViewDataRequest
{
    private ?string $query = null;

    /** @var array<ViewDataProperty> */
    private array $requirements = [];

    private ViewDataRequestState $state = ViewDataRequestState::BUILDING;

    /** @var string[] */
    private array $componentIds;

    private ViewDataConditionCollection $requestConditions;

    /** @var array<string, self> */
    private array $componentRequests = [];

    /** @var array<string, ViewDataRequestInterface> */
    private array $dataRequests;

    /**
     * @param ComponentInterface[] $components
     */
    public function __construct(
        private readonly array $components = []
    )
    {
        $this->requestConditions = new ViewDataConditionCollection();
        $this->dataRequests = [
            BingAdsViewDataRequest::getType()               => new BingAdsViewDataRequest(),
            ContentPageViewDataRequest::getType()           => new ContentPageViewDataRequest(),
            ContentPagesViewDataRequest::getType()          => new ContentPagesViewDataRequest(),
            ContentPageCategoryViewDataRequest::getType()   => new ContentPageCategoryViewDataRequest(),
            ContentPageCategoriesViewDataRequest::getType() => new ContentPageCategoriesViewDataRequest(),
            GoogleCsaViewDataRequest::getType()             => new GoogleCsaViewDataRequest(),
            ImageViewDataRequest::getType()                 => new ImageViewDataRequest(),
            NewsViewDataRequest::getType()                  => new NewsViewDataRequest(),
            OrganicViewDataRequest::getType()               => new OrganicViewDataRequest(),
            RelatedTermsViewDataRequest::getType()          => new RelatedTermsViewDataRequest(),
        ];
    }

    public function getQuery(): ?string
    {
        return $this->query;
    }

    public function setQuery(?string $query): self
    {
        $this->query = $query;

        return $this;
    }

    /**
     * @return ComponentInterface[]
     */
    public function getComponents(): array
    {
        return $this->components;
    }

    /**
     * @return string[]
     */
    public function getComponentIds(): array
    {
        if (!isset($this->componentIds)) {
            $this->componentIds = array_map(
                static fn (ComponentInterface $component) => $component->getId(),
                $this->components,
            );
        }

        return $this->componentIds;
    }

    /**
     * @return ViewDataProperty[]
     */
    public function getRequirements(): array
    {
        return $this->requirements;
    }

    /**
     * @param ViewDataProperty[] $requirements
     */
    public function setRequirements(array $requirements): self
    {
        foreach ($requirements as $requirement) {
            if (!in_array($requirement, $this->requirements, true)) {
                $this->requirements[] = $requirement;
            }
        }

        return $this;
    }

    public function createForComponent(ComponentInterface $component): self
    {
        $request = new self(
            components: [$component],
        );

        $this->componentRequests[$component->getId()] = $request;

        return $request;
    }

    public function bingAds(): BingAdsViewDataRequest
    {
        return $this->getDataRequest(
            BingAdsViewDataRequest::getType(),
            BingAdsViewDataRequest::class
        );
    }

    public function contentPage(): ContentPageViewDataRequest
    {
        return $this->getDataRequest(
            ContentPageViewDataRequest::getType(),
            ContentPageViewDataRequest::class
        );
    }

    public function contentPages(): ContentPagesViewDataRequest
    {
        return $this->getDataRequest(
            ContentPagesViewDataRequest::getType(),
            ContentPagesViewDataRequest::class
        );
    }

    public function contentPageCategory(): ContentPageCategoryViewDataRequest
    {
        return $this->getDataRequest(
            ContentPageCategoryViewDataRequest::getType(),
            ContentPageCategoryViewDataRequest::class
        );
    }

    public function contentPageCategories(): ContentPageCategoriesViewDataRequest
    {
        return $this->getDataRequest(
            ContentPageCategoriesViewDataRequest::getType(),
            ContentPageCategoriesViewDataRequest::class
        );
    }

    public function googleCsa(): GoogleCsaViewDataRequest
    {
        return $this->getDataRequest(
            GoogleCsaViewDataRequest::getType(),
            GoogleCsaViewDataRequest::class
        );
    }

    public function image(): ImageViewDataRequest
    {
        return $this->getDataRequest(
            ImageViewDataRequest::getType(),
            ImageViewDataRequest::class
        );
    }

    public function news(): NewsViewDataRequest
    {
        return $this->getDataRequest(
            NewsViewDataRequest::getType(),
            NewsViewDataRequest::class
        );
    }

    public function organic(): OrganicViewDataRequest
    {
        return $this->getDataRequest(
            OrganicViewDataRequest::getType(),
            OrganicViewDataRequest::class
        );
    }

    public function relatedTerms(): RelatedTermsViewDataRequest
    {
        return $this->getDataRequest(
            RelatedTermsViewDataRequest::getType(),
            RelatedTermsViewDataRequest::class
        );
    }

    /**
     * @template T of ViewDataRequestInterface
     *
     * @param class-string<T> $expectedClass
     *
     * @return T
     */
    private function getDataRequest(string $type, string $expectedClass): ViewDataRequestInterface
    {
        if (!$this->dataRequests[$type] instanceof $expectedClass) {
            throw new \RuntimeException(
                sprintf('Invalid view data request set for "%s".', $type)
            );
        }

        return $this->dataRequests[$type];
    }

    /**
     * @return array<string, ViewDataRequestInterface>
     */
    public function getDataRequests(): array
    {
        return $this->dataRequests;
    }

    public function finalize(ViewDataRegistry $viewDataRegistry): void
    {
        if ($this->state === ViewDataRequestState::FINALIZED) {
            return;
        }

        foreach ($this->componentRequests as $index => $componentRequest) {
            $conditions = $componentRequest->getRequestConditions();

            if ($conditions->check($viewDataRegistry)) {
                $this->merge($componentRequest);
            }

            unset($this->componentRequests[$index]);
        }

        $this->state = ViewDataRequestState::FINALIZED;
    }

    public function merge(self $viewDataRequest): self
    {
        return $this->setRequirements($viewDataRequest->getRequirements())
            ->mergeDataRequests($viewDataRequest->getDataRequests());
    }

    /**
     * @param array<ViewDataRequestInterface> $dataRequests
     */
    public function mergeDataRequests(array $dataRequests): self
    {
        foreach ($dataRequests as $dataRequest) {
            if (isset($this->dataRequests[$dataRequest::getType()])) {
                $this->dataRequests[$dataRequest::getType()]->mergeWith([$dataRequest]);
            }
        }

        return $this;
    }

    public function setRequestConditions(ViewDataConditionCollection $conditions): self
    {
        $this->requestConditions = $conditions;

        return $this;
    }

    public function getRequestConditions(): ViewDataConditionCollection
    {
        return $this->requestConditions;
    }
}
