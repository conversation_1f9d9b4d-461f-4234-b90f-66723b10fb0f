<?php

declare(strict_types=1);

namespace App\OneTrust\EventSubscriber;

use App\OneTrust\Helper\OneTrustHelper;
use App\SplitTest\SplitTestExtendedReaderInterface;
use App\Template\Event\RenderTemplateHeadersEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Twig\Environment;

readonly class InjectOneTrustScriptsEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private OneTrustHelper $oneTrustHelper,
        private SplitTestExtendedReaderInterface $splitTestExtendedReader,
        private Environment $twig
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            // OneTrust script should be loaded before anything else
            RenderTemplateHeadersEvent::NAME => ['renderTemplateHeaders', 2500],
        ];
    }

    public function renderTemplateHeaders(RenderTemplateHeadersEvent $event): void
    {
        if (!$this->oneTrustHelper->isEnabled()) {
            return;
        }

        $event->addItem(
            $this->twig->render(
                '@theme/onetrust/onetrust_script_html.html.twig',
                [
                    'domain_script_id' => $this->oneTrustHelper->getDomainScriptId(),
                    'add_tcf_stub'     => !$this->splitTestExtendedReader->isVariantActive('enc')
                                          && !$this->splitTestExtendedReader->isVariantActive('encwi'),
                ],
            ),
        );
    }
}
